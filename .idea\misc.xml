<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="NullableNotNullManager">
    <option name="myDefaultNullable" value="androidx.annotation.Nullable" />
    <option name="myDefaultNotNull" value="androidx.annotation.NonNull" />
    <option name="myNullables">
      <value>
        <list size="15">
          <item index="0" class="java.lang.String" itemvalue="org.jspecify.annotations.Nullable" />
          <item index="1" class="java.lang.String" itemvalue="com.android.annotations.Nullable" />
          <item index="2" class="java.lang.String" itemvalue="androidx.annotation.RecentlyNullable" />
          <item index="3" class="java.lang.String" itemvalue="org.checkerframework.checker.nullness.compatqual.NullableDecl" />
          <item index="4" class="java.lang.String" itemvalue="org.jetbrains.annotations.Nullable" />
          <item index="5" class="java.lang.String" itemvalue="androidx.annotation.Nullable" />
          <item index="6" class="java.lang.String" itemvalue="org.eclipse.jdt.annotation.Nullable" />
          <item index="7" class="java.lang.String" itemvalue="edu.umd.cs.findbugs.annotations.Nullable" />
          <item index="8" class="java.lang.String" itemvalue="android.support.annotation.Nullable" />
          <item index="9" class="java.lang.String" itemvalue="jakarta.annotation.Nullable" />
          <item index="10" class="java.lang.String" itemvalue="javax.annotation.CheckForNull" />
          <item index="11" class="java.lang.String" itemvalue="javax.annotation.Nullable" />
          <item index="12" class="java.lang.String" itemvalue="org.checkerframework.checker.nullness.qual.Nullable" />
          <item index="13" class="java.lang.String" itemvalue="org.checkerframework.checker.nullness.compatqual.NullableType" />
          <item index="14" class="java.lang.String" itemvalue="android.annotation.Nullable" />
        </list>
      </value>
    </option>
    <option name="myNotNulls">
      <value>
        <list size="15">
          <item index="0" class="java.lang.String" itemvalue="androidx.annotation.RecentlyNonNull" />
          <item index="1" class="java.lang.String" itemvalue="org.checkerframework.checker.nullness.qual.NonNull" />
          <item index="2" class="java.lang.String" itemvalue="org.jspecify.annotations.NonNull" />
          <item index="3" class="java.lang.String" itemvalue="jakarta.annotation.Nonnull" />
          <item index="4" class="java.lang.String" itemvalue="androidx.annotation.NonNull" />
          <item index="5" class="java.lang.String" itemvalue="org.checkerframework.checker.nullness.compatqual.NonNullType" />
          <item index="6" class="java.lang.String" itemvalue="android.support.annotation.NonNull" />
          <item index="7" class="java.lang.String" itemvalue="com.android.annotations.NonNull" />
          <item index="8" class="java.lang.String" itemvalue="edu.umd.cs.findbugs.annotations.NonNull" />
          <item index="9" class="java.lang.String" itemvalue="org.checkerframework.checker.nullness.compatqual.NonNullDecl" />
          <item index="10" class="java.lang.String" itemvalue="org.jetbrains.annotations.NotNull" />
          <item index="11" class="java.lang.String" itemvalue="javax.annotation.Nonnull" />
          <item index="12" class="java.lang.String" itemvalue="org.eclipse.jdt.annotation.NonNull" />
          <item index="13" class="java.lang.String" itemvalue="android.annotation.NonNull" />
          <item index="14" class="java.lang.String" itemvalue="lombok.NonNull" />
        </list>
      </value>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_21" default="true" project-jdk-name="jbr-21" project-jdk-type="JavaSDK" />
</project>